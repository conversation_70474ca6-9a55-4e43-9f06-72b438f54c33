package test

import (
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	ziaHttp "ziaacademy-backend/cmd/server/http"
	dbPkg "ziaacademy-backend/db"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var db *gorm.DB
var router, noAuthRouter *gin.Engine

func cleanupTestData() {
	// Clean up test data to avoid unique constraint violations
	// Delete in order to respect foreign key constraints
	db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE test_type_id IN (SELECT id FROM test_types WHERE name LIKE '%Mock%' OR name LIKE 'Test %'))")
	db.Exec("DELETE FROM tests WHERE test_type_id IN (SELECT id FROM test_types WHERE name LIKE '%Mock%' OR name LIKE 'Test %')")
	db.Exec("DELETE FROM videos WHERE name LIKE 'test_%'")
	db.Exec("DELETE FROM chapters WHERE name LIKE 'Test %'")
	db.Exec("DELETE FROM test_types WHERE name LIKE '%Mock%' OR name LIKE 'Test %'")
	db.Exec("DELETE FROM section_types WHERE name LIKE '%Section' OR name LIKE 'Test %'")
	db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name LIKE 'Test %' OR name = 'Mathematics' OR name = 'Chemistry 12th' OR name = 'Physics Test' OR name LIKE '% Test Formula')")
	db.Exec("DELETE FROM previous_year_papers WHERE exam_type IN ('IIT-JEE', 'NEET')")
	db.Exec("DELETE FROM subjects WHERE name LIKE 'Test %' OR name = 'Mathematics' OR name = 'Chemistry 12th' OR name = 'Physics Test' OR name LIKE '% Test Formula'")
	db.Exec("DELETE FROM users WHERE email LIKE '%@example.com'")
}

func TestMain(m *testing.M) {
	dsn := "host=localhost user=postgres password=postgres dbname=mydatabase port=5432 sslmode=disable"
	var err error
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to DB: %v", err)
	}

	server := dbPkg.NewServer(db)

	// Setup router with skipAuth=true for testing
	router = ziaHttp.SetupRouter(server, os.Stdout, false)
	noAuthRouter = ziaHttp.SetupRouter(server, os.Stdout, true)

	// Clean up test data before running tests
	cleanupTestData()

	code := m.Run()

	// Optional: cleanup
	sqlDB, _ := db.DB()
	sqlDB.Close()
	os.Exit(code)
}

func TestCreateVideo(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM videos WHERE name = ?", "test_video")

	// First create a subject and chapter for the video
	subject := models.Subject{
		Name:        "Test Subject",
		DisplayName: "Test Subject Display",
	}
	db.Create(&subject)

	chapter := models.Chapter{
		Name:        "Test Chapter",
		DisplayName: "Test Chapter Display",
		SubjectID:   subject.ID,
	}
	db.Create(&chapter)

	videoInput := models.VideoForCreate{
		Name:        "test_video",
		DisplayName: "Test Video Display",
		VideoUrl:    "http://example.com/video.mp4",
		ChapterName: "Test Chapter",
	}

	body, _ := json.Marshal(videoInput)
	req, _ := http.NewRequest("POST", "/api/videos", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify entry in DB
	var savedVideo models.Video
	result := db.First(&savedVideo, "name = ?", "test_video")
	assert.Nil(t, result.Error)
	assert.Equal(t, videoInput.DisplayName, savedVideo.DisplayName)
	assert.Equal(t, videoInput.VideoUrl, savedVideo.VideoUrl)

	// Cleanup
	db.Delete(&savedVideo)
	db.Delete(&chapter)
	db.Delete(&subject)
}

func TestCreateStudentWithCourses(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM students WHERE parent_email = ?", "<EMAIL>")
	db.Exec("DELETE FROM users WHERE email = ?", "<EMAIL>")

	// Build student payload using the correct structure
	payload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Student User",
			Email:          "<EMAIL>",
			PhoneNumber:    "1234567890",
			ContactAddress: "123 Student Street",
		},
		ParentPhone: "555-1234",
		ParentEmail: "<EMAIL>",
	}
	body, _ := json.Marshal(payload)

	// Prepare request
	req, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	var res models.CreatedStudentResponse
	err := json.NewDecoder(resp.Body).Decode(&res)
	assert.NoError(t, err)
	// Assertions
	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify: Student was created
	var student models.Student
	err = db.Preload("User").First(&student, "id = ?", res.CreatedStudent.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, "<EMAIL>", student.ParentEmail)
	assert.Equal(t, "Student User", student.User.FullName)
	assert.Equal(t, "<EMAIL>", student.User.Email)

	// Cleanup
	db.Delete(&student)
	db.Delete(&student.User)
}

func TestCreateAdmin(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM users WHERE email = ?", "<EMAIL>")

	adminPayload := models.AdminForCreate{
		FullName:       "Admin User",
		Email:          "<EMAIL>",
		PhoneNumber:    "9876543210",
		ContactAddress: "456 Admin Street",
		Password:       "adminpassword123",
	}

	body, _ := json.Marshal(adminPayload)
	req, _ := http.NewRequest("POST", "/api/admins", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusCreated, resp.Code)

	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.NotNil(t, response["token"])
	assert.NotNil(t, response["admin"])

	// Verify admin was created in database
	var savedAdmin models.User
	result := db.First(&savedAdmin, "email = ?", "<EMAIL>")
	assert.Nil(t, result.Error)
	assert.Equal(t, "Admin User", savedAdmin.FullName)
	assert.Equal(t, "<EMAIL>", savedAdmin.Email)
	assert.Equal(t, "9876543210", savedAdmin.PhoneNumber)
	assert.Equal(t, "Admin", savedAdmin.Role)
	assert.NotEmpty(t, savedAdmin.PasswordHash) // Password should be hashed

	// Cleanup
	db.Delete(&savedAdmin)
}
