basePath: /api/
definitions:
  gorm.DeletedAt:
    properties:
      time:
        type: string
      valid:
        description: Valid is true if Time is not NULL
        type: boolean
    type: object
  http.HTTPError:
    properties:
      code:
        example: 400
        type: integer
      message:
        example: status bad request
        type: string
    type: object
  models.AdminForCreate:
    properties:
      contact_address:
        type: string
      email:
        type: string
      full_name:
        type: string
      password:
        minLength: 6
        type: string
      phone_number:
        type: string
    required:
    - email
    - full_name
    - password
    - phone_number
    type: object
  models.Chapter:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      studyMaterials:
        items:
          $ref: '#/definitions/models.StudyMaterial'
        type: array
      subject:
        $ref: '#/definitions/models.Subject'
      subjectID:
        type: integer
      updatedAt:
        type: string
      videos:
        items:
          $ref: '#/definitions/models.Video'
        type: array
    type: object
  models.ChapterForCreate:
    properties:
      displayName:
        type: string
      name:
        type: string
      subjectName:
        type: string
    type: object
  models.Content:
    properties:
      chapterName:
        type: string
      pdfs:
        items:
          $ref: '#/definitions/models.StudyMaterial'
        type: array
      videos:
        items:
          $ref: '#/definitions/models.Video'
        type: array
    type: object
  models.Course:
    properties:
      courseType:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      discount:
        type: number
      durationInDays:
        type: integer
      id:
        type: integer
      isFree:
        type: boolean
      name:
        type: string
      price:
        type: integer
      subjects:
        items:
          $ref: '#/definitions/models.Subject'
        type: array
      tests:
        items:
          $ref: '#/definitions/models.Test'
        type: array
      updatedAt:
        type: string
    type: object
  models.CourseForCreate:
    properties:
      courseType:
        type: string
      description:
        type: string
      discount:
        type: number
      durationInDays:
        type: integer
      isFree:
        type: boolean
      name:
        type: string
      price:
        type: integer
      subjects:
        items:
          $ref: '#/definitions/models.SubjectForCreate'
        type: array
    type: object
  models.CourseWithPurchased:
    properties:
      courseType:
        type: string
      description:
        type: string
      discount:
        type: number
      durationInDays:
        type: integer
      isFree:
        type: boolean
      name:
        type: string
      price:
        type: integer
      purchased:
        type: boolean
    type: object
  models.CoursesByCategory:
    properties:
      free_courses:
        items:
          $ref: '#/definitions/models.CoursesByType'
        type: array
      paid_courses:
        items:
          $ref: '#/definitions/models.CoursesByType'
        type: array
    type: object
  models.CoursesByType:
    properties:
      course_type:
        type: string
      courses:
        items:
          $ref: '#/definitions/models.CourseWithPurchased'
        type: array
    type: object
  models.CreatedStudentResponse:
    properties:
      createdStudent:
        $ref: '#/definitions/models.Student'
      token:
        type: string
    type: object
  models.Credentials:
    properties:
      password:
        type: string
      user_email:
        type: string
    type: object
  models.Difficulty:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      name:
        type: string
      questions:
        items:
          $ref: '#/definitions/models.Question'
        type: array
      updatedAt:
        type: string
    type: object
  models.FormulaCard:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      imageUrl:
        type: string
      name:
        type: string
      subject:
        $ref: '#/definitions/models.Subject'
      subjectID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.FormulaCardForCreate:
    properties:
      image_url:
        type: string
      name:
        type: string
    required:
    - image_url
    - name
    type: object
  models.FormulaCardsForCreate:
    properties:
      formula_cards:
        items:
          $ref: '#/definitions/models.FormulaCardForCreate'
        minItems: 1
        type: array
      subject_name:
        type: string
    required:
    - formula_cards
    - subject_name
    type: object
  models.MaterialForCreate:
    properties:
      chapterName:
        type: string
      displayName:
        type: string
      name:
        type: string
      url:
        type: string
    type: object
  models.Option:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      isCorrect:
        description: Correctness flag
        type: boolean
      optionImageURL:
        description: Optional image URL
        type: string
      optionText:
        description: Option content
        type: string
      question:
        allOf:
        - $ref: '#/definitions/models.Question'
        description: GORM association
      questionID:
        description: Foreign key to Questions
        type: integer
      updatedAt:
        type: string
    type: object
  models.OptionForCreate:
    properties:
      is_correct:
        type: boolean
      option_image_url:
        type: string
      option_text:
        type: string
    required:
    - option_text
    type: object
  models.PreviousYearPaper:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      examType:
        type: string
      id:
        type: integer
      month:
        type: integer
      pdfUrl:
        type: string
      updatedAt:
        type: string
      year:
        type: integer
    type: object
  models.PreviousYearPaperForCreate:
    properties:
      exam_type:
        enum:
        - IIT-JEE
        - NEET
        type: string
      month:
        maximum: 12
        minimum: 1
        type: integer
      pdf_url:
        type: string
      year:
        maximum: 2100
        minimum: 1900
        type: integer
    required:
    - exam_type
    - month
    - pdf_url
    - year
    type: object
  models.PreviousYearPapersForCreate:
    properties:
      papers:
        items:
          $ref: '#/definitions/models.PreviousYearPaperForCreate'
        minItems: 1
        type: array
    required:
    - papers
    type: object
  models.Question:
    properties:
      correctAnswer:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      difficulty:
        $ref: '#/definitions/models.Difficulty'
      difficultyID:
        type: integer
      fileUrl:
        type: string
      id:
        type: integer
      imageUrl:
        type: string
      options:
        description: One-to-many relationship with options
        items:
          $ref: '#/definitions/models.Option'
        type: array
      questionType:
        type: string
      text:
        type: string
      topic:
        $ref: '#/definitions/models.Topic'
      topicID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.QuestionForCreate:
    properties:
      correct_answer:
        type: string
      difficulty_name:
        type: string
      file_url:
        type: string
      image_url:
        type: string
      options:
        items:
          $ref: '#/definitions/models.OptionForCreate'
        type: array
      question_type:
        type: string
      text:
        type: string
      topic_name:
        type: string
    required:
    - difficulty_name
    - question_type
    - text
    - topic_name
    type: object
  models.Section:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      questions:
        items:
          $ref: '#/definitions/models.Question'
        type: array
      sectionType:
        $ref: '#/definitions/models.SectionType'
      sectionTypeID:
        type: integer
      test:
        $ref: '#/definitions/models.Test'
      testID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.SectionForCreate:
    properties:
      displayName:
        type: string
      name:
        type: string
      sectionTypeName:
        type: string
    type: object
  models.SectionType:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      name:
        type: string
      negativeMarks:
        type: number
      positiveMarks:
        type: number
      questionCount:
        type: integer
      subject:
        $ref: '#/definitions/models.Subject'
      subjectID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.SectionTypeForCreate:
    properties:
      name:
        type: string
      negativeMarks:
        type: number
      positiveMarks:
        type: number
      questionCount:
        type: integer
      subjectName:
        type: string
    type: object
  models.Student:
    properties:
      courses:
        items:
          $ref: '#/definitions/models.Course'
        type: array
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      parent_email:
        type: string
      parent_phone:
        type: string
      updatedAt:
        type: string
      user:
        $ref: '#/definitions/models.User'
      userID:
        type: integer
    type: object
  models.StudentEvaluationResult:
    properties:
      correct_answers:
        type: integer
      evaluation_time:
        type: string
      message:
        type: string
      student_id:
        type: integer
      student_name:
        type: string
      total_questions:
        type: integer
      total_score:
        type: integer
    type: object
  models.StudentForCreate:
    properties:
      contactAddress:
        type: string
      email:
        type: string
      fullName:
        type: string
      parent_email:
        type: string
      parent_phone:
        type: string
      phoneNumber:
        type: string
    type: object
  models.StudentRankingInfo:
    properties:
      final_marks:
        type: integer
      percentile:
        type: number
      rank:
        type: integer
      student_email:
        type: string
      student_id:
        type: integer
      student_name:
        type: string
      total_negative_marks:
        type: integer
      total_positive_marks:
        type: integer
    type: object
  models.StudyMaterial:
    properties:
      chapterID:
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      updatedAt:
        type: string
      url:
        type: string
    type: object
  models.Subject:
    properties:
      chapters:
        items:
          $ref: '#/definitions/models.Chapter'
        type: array
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      formulaCards:
        items:
          $ref: '#/definitions/models.FormulaCard'
        type: array
      id:
        type: integer
      name:
        type: string
      updatedAt:
        type: string
    type: object
  models.SubjectForCreate:
    properties:
      displayName:
        type: string
      name:
        type: string
    type: object
  models.Test:
    properties:
      active:
        type: boolean
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      fromTime:
        type: string
      id:
        type: integer
      name:
        type: string
      sections:
        items:
          $ref: '#/definitions/models.Section'
        type: array
      testType:
        $ref: '#/definitions/models.TestType'
      testTypeID:
        type: integer
      toTime:
        type: string
      updatedAt:
        type: string
    type: object
  models.TestEvaluationRequest:
    properties:
      test_id:
        type: integer
    required:
    - test_id
    type: object
  models.TestEvaluationResult:
    properties:
      message:
        type: string
      student_results:
        items:
          $ref: '#/definitions/models.StudentEvaluationResult'
        type: array
      test_id:
        type: integer
      test_name:
        type: string
      total_students_evaluated:
        type: integer
    type: object
  models.TestForCreate:
    properties:
      description:
        type: string
      fromTime:
        type: string
      name:
        type: string
      sections:
        items:
          $ref: '#/definitions/models.SectionForCreate'
        type: array
      testTypeName:
        type: string
      toTime:
        type: string
    type: object
  models.TestRankingResult:
    properties:
      average_marks:
        type: number
      highest_marks:
        type: integer
      lowest_marks:
        type: integer
      message:
        type: string
      student_rankings:
        items:
          $ref: '#/definitions/models.StudentRankingInfo'
        type: array
      test_id:
        type: integer
      test_name:
        type: string
      total_students:
        type: integer
    type: object
  models.TestResponse:
    properties:
      calculatedScore:
        description: Nullable, can be set after evaluation
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      isCorrect:
        description: Automatically evaluated
        type: boolean
      question:
        $ref: '#/definitions/models.Question'
      questionID:
        description: FK to questions
        type: integer
      responseText:
        description: Nullable for text answers
        type: string
      selectedOptionIDs:
        description: PostgreSQL array type (use pgx or pq)
        items:
          type: integer
        type: array
      student:
        allOf:
        - $ref: '#/definitions/models.Student'
        description: Assumes you have a Student model
      studentID:
        description: FK to students
        type: integer
      test:
        $ref: '#/definitions/models.Test'
      testID:
        description: FK to tests
        type: integer
      updatedAt:
        type: string
    type: object
  models.TestResponseForCreate:
    properties:
      question_id:
        type: integer
      response_text:
        type: string
      selected_option_ids:
        items:
          type: integer
        type: array
    required:
    - question_id
    type: object
  models.TestResponseResult:
    properties:
      calculated_score:
        type: integer
      is_correct:
        type: boolean
      message:
        type: string
      question_id:
        type: integer
    type: object
  models.TestResponsesForCreate:
    properties:
      responses:
        items:
          $ref: '#/definitions/models.TestResponseForCreate'
        minItems: 1
        type: array
      test_id:
        type: integer
    required:
    - responses
    - test_id
    type: object
  models.TestResponsesResult:
    properties:
      correct_answers:
        type: integer
      message:
        type: string
      response_results:
        items:
          $ref: '#/definitions/models.TestResponseResult'
        type: array
      student_id:
        type: integer
      test_id:
        type: integer
      total_questions:
        type: integer
      total_score:
        type: integer
    type: object
  models.TestType:
    properties:
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      sectionTypes:
        items:
          $ref: '#/definitions/models.SectionType'
        type: array
      updatedAt:
        type: string
    type: object
  models.TestTypeForCreate:
    properties:
      name:
        type: string
      sectionTypeNames:
        items:
          type: string
        type: array
    type: object
  models.Topic:
    properties:
      chapter:
        $ref: '#/definitions/models.Chapter'
      chapterID:
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      name:
        type: string
      questions:
        items:
          $ref: '#/definitions/models.Question'
        type: array
      updatedAt:
        type: string
    type: object
  models.TopicForCreate:
    properties:
      chapterName:
        type: string
      name:
        type: string
    type: object
  models.UpdatePassword:
    properties:
      new_password:
        type: string
    type: object
  models.User:
    properties:
      contactAddress:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      email:
        type: string
      emailVerified:
        type: boolean
      fullName:
        type: string
      id:
        type: integer
      passwordHash:
        type: string
      phoneNumber:
        type: string
      phoneVerified:
        type: boolean
      role:
        type: string
      updatedAt:
        type: string
    type: object
  models.Video:
    properties:
      chapterID:
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      updatedAt:
        type: string
      videoUrl:
        type: string
      viewCount:
        type: integer
    type: object
  models.VideoForCreate:
    properties:
      chapterName:
        type: string
      displayName:
        type: string
      name:
        type: string
      videoUrl:
        type: string
    type: object
host: ************:443
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Backend server for ZIA Academy.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: ZIA Academy App
  version: "1.0"
paths:
  /admins:
    post:
      consumes:
      - application/json
      description: Create a new admin user with role "Admin"
      parameters:
      - description: Admin user details
        in: body
        name: admin
        required: true
        schema:
          $ref: '#/definitions/models.AdminForCreate'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.User'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Create Admin User
      tags:
      - admins
  /chapters:
    get:
      consumes:
      - application/json
      description: get chapters for a subject_id
      parameters:
      - description: Subject ID
        in: query
        name: subject_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Chapter'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Chapters
      tags:
      - chapters
    post:
      consumes:
      - application/json
      description: create new chapter
      parameters:
      - description: chapter details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.ChapterForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Chapter'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateChapters
      tags:
      - chapters
  /content:
    get:
      consumes:
      - application/json
      description: get videos and studymaterial for given chapter
      parameters:
      - description: Chapter ID
        in: query
        name: chapter_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Content'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Content
      tags:
      - questions
  /courses:
    get:
      consumes:
      - application/json
      description: get courses for the logged in student
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CoursesByCategory'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Courses
      tags:
      - courses
    post:
      consumes:
      - application/json
      description: create new course
      parameters:
      - description: course details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.CourseForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Course'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateCourse
      tags:
      - courses
  /courses/{course_id}/tests/{test_id}:
    post:
      consumes:
      - application/json
      description: associate an existing test with an existing course
      parameters:
      - description: Course ID
        in: path
        name: course_id
        required: true
        type: integer
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Associate Test with Course
      tags:
      - courses
  /enroll/{course_id}:
    post:
      consumes:
      - application/json
      description: enroll student in a course
      parameters:
      - description: course ID to enroll in
        in: path
        name: course_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Student'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: EnrollInCourse
      tags:
      - students
  /formula-cards:
    get:
      consumes:
      - application/json
      description: get formula cards for a specific subject
      parameters:
      - description: Subject Name
        in: query
        name: subject_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.FormulaCard'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Formula Cards by Subject
      tags:
      - formula-cards
    post:
      consumes:
      - application/json
      description: create multiple formula cards for a subject
      parameters:
      - description: formula cards details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.FormulaCardsForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.FormulaCard'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateFormulaCards
      tags:
      - formula-cards
  /login:
    post:
      consumes:
      - application/json
      description: login with email and password
      parameters:
      - description: user_email and password
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.Credentials'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      summary: Login
      tags:
      - login
  /previous-year-papers:
    get:
      consumes:
      - application/json
      description: get previous year papers filtered by exam type, sorted by year
        (descending)
      parameters:
      - description: Exam Type (IIT-JEE or NEET)
        in: query
        name: exam_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.PreviousYearPaper'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Previous Year Papers by Exam Type
      tags:
      - previous-year-papers
    post:
      consumes:
      - application/json
      description: create multiple previous year papers in bulk
      parameters:
      - description: previous year papers details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.PreviousYearPapersForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.PreviousYearPaper'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreatePreviousYearPapers
      tags:
      - previous-year-papers
  /questions:
    get:
      consumes:
      - application/json
      description: get questions for given topic and difficulty
      parameters:
      - description: Topic name
        in: query
        name: topic
        required: true
        type: string
      - description: Difficulty ()
        in: query
        name: difficulty
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Question'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Questions
      tags:
      - questions
    post:
      consumes:
      - application/json
      description: create new question
      parameters:
      - description: question details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.QuestionForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Question'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateQuestion
      tags:
      - questions
  /section-types:
    post:
      consumes:
      - application/json
      description: create new section type
      parameters:
      - description: section type details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.SectionTypeForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SectionType'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateSectionType
      tags:
      - tests
  /students:
    post:
      consumes:
      - application/json
      description: create new student
      parameters:
      - description: student details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.StudentForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CreatedStudentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      summary: CreateStudent
      tags:
      - students
  /studymaterials:
    post:
      consumes:
      - application/json
      description: add a new material
      parameters:
      - description: material details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.MaterialForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StudyMaterial'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: AddStudyMaterial
      tags:
      - library
  /subjects:
    get:
      consumes:
      - application/json
      description: get subjects for logged in student
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Subject'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Subjects
      tags:
      - library
    post:
      consumes:
      - application/json
      description: create new subject
      parameters:
      - description: subject details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.SubjectForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Subject'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateSubject
      tags:
      - subjects
  /test-responses:
    post:
      consumes:
      - application/json
      description: Record student responses for all questions in a test
      parameters:
      - description: test responses
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestResponsesForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestResponsesResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: RecordTestResponses
      tags:
      - test-responses
  /test-responses/{test_id}:
    get:
      consumes:
      - application/json
      description: Get student responses for a specific test
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TestResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: GetStudentTestResponses
      tags:
      - test-responses
  /test-responses/evaluate:
    post:
      consumes:
      - application/json
      description: Evaluate all unevaluated responses for a specific test (Admin only)
      parameters:
      - description: test evaluation request
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestEvaluationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestEvaluationResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: EvaluateTestResponses
      tags:
      - test-responses
  /test-responses/rankings/{test_id}:
    get:
      consumes:
      - application/json
      description: Get rankings for all students in a specific test
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      - description: 'Limit number of results (default: 100)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestRankingResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: GetTestRankings
      tags:
      - test-responses
  /test-types:
    post:
      consumes:
      - application/json
      description: create new test type
      parameters:
      - description: test type details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestTypeForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestType'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateTestType
      tags:
      - tests
  /tests:
    get:
      consumes:
      - application/json
      description: get tests for the logged in user. Students see only tests from
        enrolled courses. Admins see all tests.
      parameters:
      - description: Filter by active status (true/false)
        in: query
        name: active
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Test'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Tests
      tags:
      - tests
    post:
      consumes:
      - application/json
      description: create new test of a given type
      parameters:
      - description: test details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Test'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateTest
      tags:
      - tests
  /tests/{test_id}/questions:
    post:
      consumes:
      - application/json
      description: add questions to a test
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      - description: question IDs
        in: body
        name: item
        required: true
        schema:
          items:
            type: integer
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: AddQuestionsToTest
      tags:
      - tests
  /topics:
    post:
      consumes:
      - application/json
      description: create new topic for questions
      parameters:
      - description: topic details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TopicForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Topic'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateTopic
      tags:
      - questions
  /users/password:
    post:
      consumes:
      - application/json
      description: update password for logged in user
      parameters:
      - description: new password
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.UpdatePassword'
      produces:
      - application/json
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: UpdatePassword
      tags:
      - users
  /videos:
    post:
      consumes:
      - application/json
      description: add a new video
      parameters:
      - description: video details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.VideoForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Video'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: AddVideo
      tags:
      - library
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
