package test

import (
	"encoding/json"
	"net/http"
	"net/url"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreatePreviousYearPapers(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM previous_year_papers WHERE exam_type = 'IIT-JEE' AND year = 2023")

	// Create previous year papers
	papersInput := models.PreviousYearPapersForCreate{
		Papers: []models.PreviousYearPaperForCreate{
			{
				Month:    1,
				Year:     2023,
				PdfUrl:   "https://example.com/iit-jee-2023-jan.pdf",
				ExamType: models.ExamTypeIITJEE,
			},
			{
				Month:    6,
				Year:     2023,
				PdfUrl:   "https://example.com/iit-jee-2023-jun.pdf",
				ExamType: models.ExamTypeIITJEE,
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/previous-year-papers", papersInput)

	// Check status code
	assert.Equal(t, http.StatusOK, rr.Code)

	// Parse response
	var response []models.PreviousYearPaper
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)

	assert.Len(t, response, 2)
	assert.Equal(t, 1, response[0].Month)
	assert.Equal(t, 2023, response[0].Year)
	assert.Equal(t, "https://example.com/iit-jee-2023-jan.pdf", response[0].PdfUrl)
	assert.Equal(t, models.ExamTypeIITJEE, response[0].ExamType)

	// Verify they were persisted in the database
	var fromDB []models.PreviousYearPaper
	err = db.Where("exam_type = ? AND year = ?", models.ExamTypeIITJEE, 2023).Find(&fromDB).Error
	assert.Nil(t, err)
	assert.Len(t, fromDB, 2)
}

func TestGetPreviousYearPapersByExamType(t *testing.T) {
	// Clean up before test - clean up all test exam types
	testExamTypes := []string{models.ExamTypeNEET, models.ExamTypeIITJEE}
	for _, examType := range testExamTypes {
		db.Exec("DELETE FROM previous_year_papers WHERE exam_type = ?", examType)
	}

	// Create test data for multiple exam types
	examTypeData := []struct {
		examType string
		papers   []models.PreviousYearPaperForCreate
	}{
		{
			examType: models.ExamTypeNEET,
			papers: []models.PreviousYearPaperForCreate{
				{
					Month:    5,
					Year:     2022,
					PdfUrl:   "https://example.com/neet-2022-may.pdf",
					ExamType: models.ExamTypeNEET,
				},
				{
					Month:    9,
					Year:     2023,
					PdfUrl:   "https://example.com/neet-2023-sep.pdf",
					ExamType: models.ExamTypeNEET,
				},
				{
					Month:    3,
					Year:     2023,
					PdfUrl:   "https://example.com/neet-2023-mar.pdf",
					ExamType: models.ExamTypeNEET,
				},
			},
		},
		{
			examType: models.ExamTypeIITJEE,
			papers: []models.PreviousYearPaperForCreate{
				{
					Month:    1,
					Year:     2021,
					PdfUrl:   "https://example.com/iit-jee-2021-jan.pdf",
					ExamType: models.ExamTypeIITJEE,
				},
				{
					Month:    4,
					Year:     2023,
					PdfUrl:   "https://example.com/iit-jee-2023-apr.pdf",
					ExamType: models.ExamTypeIITJEE,
				},
				{
					Month:    8,
					Year:     2022,
					PdfUrl:   "https://example.com/iit-jee-2022-aug.pdf",
					ExamType: models.ExamTypeIITJEE,
				},
				{
					Month:    12,
					Year:     2023,
					PdfUrl:   "https://example.com/iit-jee-2023-dec.pdf",
					ExamType: models.ExamTypeIITJEE,
				},
			},
		},
	}

	// Create papers for each exam type
	for _, examData := range examTypeData {
		papersInput := models.PreviousYearPapersForCreate{
			Papers: examData.papers,
		}

		createRR := requestExecutionHelper(http.MethodPost, "/api/previous-year-papers", papersInput)

		assert.Equal(t, http.StatusOK, createRR.Code)
	}

	// Test getting papers for each exam type
	for _, examData := range examTypeData {
		t.Run("Get papers for "+examData.examType, func(t *testing.T) {
			getURL := "/api/previous-year-papers?exam_type=" + url.QueryEscape(examData.examType)
			getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

			// Check status code
			assert.Equal(t, http.StatusOK, getRR.Code)

			// Parse response
			var response map[string][]models.PreviousYearPaper
			err := json.Unmarshal(getRR.Body.Bytes(), &response)
			assert.Nil(t, err)

			papers := response["papers"]
			assert.Len(t, papers, len(examData.papers))

			// Verify all papers belong to the correct exam type
			for _, paper := range papers {
				assert.Equal(t, examData.examType, paper.ExamType)
			}

			// Verify sorting: should be sorted by year DESC, then month DESC
			for i := 0; i < len(papers)-1; i++ {
				current := papers[i]
				next := papers[i+1]

				// Current paper should have year >= next paper's year
				if current.Year == next.Year {
					// If same year, current month should be >= next month
					assert.GreaterOrEqual(t, current.Month, next.Month,
						"Papers should be sorted by month DESC within the same year")
				} else {
					assert.Greater(t, current.Year, next.Year,
						"Papers should be sorted by year DESC")
				}
			}
		})
	}

	// Test specific sorting for NEET papers
	t.Run("Verify NEET papers sorting", func(t *testing.T) {
		getURL := "/api/previous-year-papers?exam_type=" + url.QueryEscape(models.ExamTypeNEET)
		getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

		var response map[string][]models.PreviousYearPaper
		err := json.Unmarshal(getRR.Body.Bytes(), &response)
		assert.Nil(t, err)

		papers := response["papers"]
		assert.Len(t, papers, 3)

		// Expected order: 2023-Sep, 2023-Mar, 2022-May
		assert.Equal(t, 2023, papers[0].Year)
		assert.Equal(t, 9, papers[0].Month) // September
		assert.Equal(t, 2023, papers[1].Year)
		assert.Equal(t, 3, papers[1].Month) // March
		assert.Equal(t, 2022, papers[2].Year)
		assert.Equal(t, 5, papers[2].Month) // May
	})

	// Test specific sorting for IIT-JEE papers
	t.Run("Verify IIT-JEE papers sorting", func(t *testing.T) {
		getURL := "/api/previous-year-papers?exam_type=" + url.QueryEscape(models.ExamTypeIITJEE)
		getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

		var response map[string][]models.PreviousYearPaper
		err := json.Unmarshal(getRR.Body.Bytes(), &response)
		assert.Nil(t, err)

		papers := response["papers"]
		assert.Len(t, papers, 4)

		// Expected order: 2023-Dec, 2023-Apr, 2022-Aug, 2021-Jan
		assert.Equal(t, 2023, papers[0].Year)
		assert.Equal(t, 12, papers[0].Month) // December
		assert.Equal(t, 2023, papers[1].Year)
		assert.Equal(t, 4, papers[1].Month) // April
		assert.Equal(t, 2022, papers[2].Year)
		assert.Equal(t, 8, papers[2].Month) // August
		assert.Equal(t, 2021, papers[3].Year)
		assert.Equal(t, 1, papers[3].Month) // January
	})

	// Test that each exam type returns only its own papers (cross-verification)
	t.Run("Verify exam type isolation", func(t *testing.T) {
		// Get NEET papers
		neetURL := "/api/previous-year-papers?exam_type=" + url.QueryEscape(models.ExamTypeNEET)
		neetRR := requestExecutionHelper(http.MethodGet, neetURL, nil)

		var neetResponse map[string][]models.PreviousYearPaper
		err := json.Unmarshal(neetRR.Body.Bytes(), &neetResponse)
		assert.Nil(t, err)
		neetPapers := neetResponse["papers"]

		// Get IIT-JEE papers
		iitjeeURL := "/api/previous-year-papers?exam_type=" + url.QueryEscape(models.ExamTypeIITJEE)
		iitjeeRR := requestExecutionHelper(http.MethodGet, iitjeeURL, nil)

		var iitjeeResponse map[string][]models.PreviousYearPaper
		err = json.Unmarshal(iitjeeRR.Body.Bytes(), &iitjeeResponse)
		assert.Nil(t, err)
		iitjeePapers := iitjeeResponse["papers"]

		// Verify different counts
		assert.Equal(t, 3, len(neetPapers))
		assert.Equal(t, 4, len(iitjeePapers))

		// Verify no cross-contamination
		for _, paper := range neetPapers {
			assert.Equal(t, models.ExamTypeNEET, paper.ExamType)
		}
		for _, paper := range iitjeePapers {
			assert.Equal(t, models.ExamTypeIITJEE, paper.ExamType)
		}
	})
}

func TestCreatePreviousYearPapersInvalidExamType(t *testing.T) {
	// Try to create papers with invalid exam type
	papersInput := models.PreviousYearPapersForCreate{
		Papers: []models.PreviousYearPaperForCreate{
			{
				Month:    1,
				Year:     2023,
				PdfUrl:   "https://example.com/invalid-exam.pdf",
				ExamType: "INVALID-EXAM",
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/previous-year-papers", papersInput)

	// Should return 400 for invalid exam type
	assert.Equal(t, http.StatusBadRequest, rr.Code)

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, "Invalid exam type. Must be either 'IIT-JEE' or 'NEET'", response["error"])
}

func TestGetPreviousYearPapersInvalidExamType(t *testing.T) {
	// Try to get papers with invalid exam type
	getURL := "/api/previous-year-papers?exam_type=INVALID-EXAM"
	getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

	// Should return 400 for invalid exam type
	assert.Equal(t, http.StatusBadRequest, getRR.Code)

	var response map[string]string
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, "Invalid exam type. Must be either 'IIT-JEE' or 'NEET'", response["error"])
}

func TestGetPreviousYearPapersMissingExamType(t *testing.T) {
	// Try to get papers without exam_type parameter
	getRR := requestExecutionHelper(http.MethodGet, "/api/previous-year-papers", nil)

	// Should return 400 for missing exam_type parameter
	assert.Equal(t, http.StatusBadRequest, getRR.Code)

	var response map[string]string
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, "Missing exam_type parameter", response["error"])
}

func TestCreatePreviousYearPapersEmptyList(t *testing.T) {
	// Try to create papers with empty list
	papersInput := models.PreviousYearPapersForCreate{
		Papers: []models.PreviousYearPaperForCreate{},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/previous-year-papers", papersInput)

	// Should return 400 for empty papers list
	assert.Equal(t, http.StatusBadRequest, rr.Code)

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)
	// The validation happens at JSON binding level, so we get a validation error message
	assert.Contains(t, response["error"], "Papers' Error:Field validation for 'Papers' failed on the 'min' tag")
}
