package http

import (
	"log/slog"
	"net/http"
	"strconv"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// CreateSectionType godoc
//
//	@Summary		CreateSectionType
//	@Description	create new section type
//	@Security       BearerAuth
//	@Param			item	body	models.SectionTypeForCreate	true	"section type details"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.SectionType
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/section-types [post]
func (h *Handlers) CreateSectionType(ctx *gin.Context) {
	sectionTypeInput := new(models.SectionTypeForCreate)
	if err := ctx.ShouldBindJSON(sectionTypeInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	sectionType := &models.SectionType{
		Name:          sectionTypeInput.Name,
		QuestionCount: sectionTypeInput.QuestionCount,
		PositiveMarks: sectionTypeInput.PositiveMarks,
		NegativeMarks: sectionTypeInput.NegativeMarks,
	}

	createdSectionType, err := h.db.CreateSectionType(ctx.Request.Context(), sectionType, sectionTypeInput.SubjectName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, createdSectionType)
}

// CreateTestType godoc
//
//	@Summary		CreateTestType
//	@Description	create new test type
//	@Security       BearerAuth
//	@Param			item	body	models.TestTypeForCreate	true	"test type details"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.TestType
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-types [post]
func (h *Handlers) CreateTestType(ctx *gin.Context) {
	testTypeInput := new(models.TestTypeForCreate)
	if err := ctx.ShouldBindJSON(testTypeInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	testType := &models.TestType{
		Name: testTypeInput.Name,
	}

	createdTestType, err := h.db.CreateTestType(ctx.Request.Context(), testType, testTypeInput.SectionTypeNames)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, createdTestType)
}

// CreateTest godoc
//
//	@Summary		CreateTest
//	@Description	create new test of a given type
//	@Security       BearerAuth
//	@Param			item	body	models.TestForCreate	true	"test details"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.Test
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests [post]
func (h *Handlers) CreateTest(ctx *gin.Context) {
	testInput := new(models.TestForCreate)
	if err := ctx.ShouldBindJSON(testInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	test := &models.Test{
		Name:        testInput.Name,
		FromTime:    testInput.FromTime,
		ToTime:      testInput.ToTime,
		Active:      true, // Default to active
		Description: testInput.Description,
	}

	createdTest, err := h.db.CreateTest(ctx.Request.Context(), test, testInput.Sections, testInput.TestTypeName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, createdTest)
}

// AddQuestionsToTest godoc
//
//	@Summary		AddQuestionsToTest
//	@Description	add questions to a test
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Param			item	body	[]uint	true	"question IDs"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]string
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests/{test_id}/questions [post]
func (h *Handlers) AddQuestionsToTest(ctx *gin.Context) {
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	var questionIDs []uint
	if err := ctx.ShouldBindJSON(&questionIDs); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(questionIDs) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No question IDs provided"})
		return
	}

	err = h.db.AddQuestionsToTest(ctx.Request.Context(), uint(testID), questionIDs)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Questions added to test successfully"})
}

// GetTests godoc
//
//	@Summary		Get Tests
//	@Description	get tests for the logged in user. Students see only tests from enrolled courses. Admins see all tests.
//	@Security       BearerAuth
//	@Param			active	query	bool	false	"Filter by active status (true/false)"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.Test
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests [get]
func (h *Handlers) GetTests(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Extract user ID from JWT token
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetTests failed - token extraction error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse active filter parameter (optional)
	var activeOnly *bool
	activeParam := ctx.Query("active")
	if activeParam != "" {
		if activeParam == "true" {
			active := true
			activeOnly = &active
		} else if activeParam == "false" {
			active := false
			activeOnly = &active
		} else {
			duration := time.Since(start)
			slog.Warn("GetTests failed - invalid active parameter",
				"user_id", userID,
				"client_ip", clientIP,
				"active_param", activeParam,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid active parameter. Use 'true' or 'false'"})
			return
		}
	}

	slog.Debug("GetTests request",
		"user_id", userID,
		"client_ip", clientIP,
		"active_filter", activeParam,
	)

	// Get tests from database with appropriate filtering
	tests, err := h.db.GetTests(ctx.Request.Context(), userID, activeOnly)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetTests failed - database error",
			"user_id", userID,
			"client_ip", clientIP,
			"active_filter", activeParam,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetTests successful",
		"user_id", userID,
		"client_ip", clientIP,
		"active_filter", activeParam,
		"test_count", len(tests),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"tests": tests})
}
