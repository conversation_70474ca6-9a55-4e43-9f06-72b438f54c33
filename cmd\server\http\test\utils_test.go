package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func requestExecutionHelper(method, url string, payload interface{}) *httptest.ResponseRecorder {
	var req *http.Request
	if payload != nil {
		body, _ := json.Marshal(payload)
		req, _ = http.NewRequest(method, url, bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, _ = http.NewRequest(method, url, nil)
	}

	rr := httptest.NewRecorder()
	// Use noAuthRouter for tests to bypass authentication
	if noAuthRouter != nil {
		noAuthRouter.ServeHTTP(rr, req)
	} else {
		router.ServeHTTP(rr, req)
	}
	return rr
}

func authenticatedRequestHelper(method, url string, payload interface{}, token string) *httptest.ResponseRecorder {
	body, _ := json.Marshal(payload)
	req, _ := http.NewRequest(method, url, bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	rr := httptest.NewRecorder()
	// Use the authenticated router for these requests
	router.ServeHTTP(rr, req)
	return rr
}

func AddCoursesWithContent(t *testing.T, token string) models.Course {
	videoData := []models.Video{
		{
			Name:        "Go Basics Video",
			DisplayName: "Intro to Go Programming Video",
			VideoUrl:    "http://example.com/video1",
		},
	}
	studyMaterialData := []models.StudyMaterial{

		{
			Name:        "Go Basics PDF",
			DisplayName: "Intro to Go Programming Material",
			Url:         "http://example.com/study-material1",
		},
	}
	chapterData := []models.Chapter{
		{
			Name:           "Chapter 1",
			DisplayName:    "Introduction to Go",
			Videos:         videoData,
			StudyMaterials: studyMaterialData,
		},
	}
	subjects := []models.Subject{
		{Name: "Introduction", DisplayName: "Intro to Go", Chapters: chapterData},
		{Name: "Advanced Topics", DisplayName: "Go Concurrency"},
	}
	courseData := models.Course{
		Name:           "Go Programming",
		Description:    "Learn Go Programming language",
		Price:          199,
		Discount:       10.0,
		DurationInDays: 30,
		IsFree:         false,
		CourseType:     models.CourseTypeIITJEE,
		Subjects:       subjects,
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/courses", courseData)
	// Check that the course was created
	assert.Equal(t, http.StatusOK, rr.Code)

	var createdCourse models.Course
	err := json.Unmarshal(rr.Body.Bytes(), &createdCourse)
	assert.Nil(t, err)
	assert.Equal(t, "Go Programming", createdCourse.Name)
	assert.Equal(t, "Learn Go Programming language", createdCourse.Description)
	return createdCourse
}

func RegisterStudent(t *testing.T) models.CreatedStudentResponse {
	user := map[string]interface{}{
		"full_name":       "John Doe",
		"email":           "<EMAIL>",
		"phone_number":    "1234567890",
		"password_hash":   "securepassword",
		"role":            "student",
		"contact_address": "123 Street",
	}
	studentPayload := map[string]interface{}{
		"user":         user,
		"parent_phone": "0987654321",
		"parent_email": "<EMAIL>",
	}
	resp := requestExecutionHelper(http.MethodPost, "/students", studentPayload)
	assert.Equal(t, http.StatusOK, resp.Code)
	var createdResp models.CreatedStudentResponse
	err := json.Unmarshal(resp.Body.Bytes(), &createdResp)
	assert.Nil(t, err)
	assert.Equal(t, "John Doe", createdResp.CreatedStudent.User.FullName)

	return createdResp
}
