package http

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// CreatePreviousYearPapers godoc
//
//	@Summary		CreatePreviousYearPapers
//	@Description	create multiple previous year papers in bulk
//	@Security       BearerAuth
//	@Param			item	body	models.PreviousYearPapersForCreate	true	"previous year papers details"
//	@Tags			previous-year-papers
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.PreviousYearPaper
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/previous-year-papers [post]
func (h *Handlers) CreatePreviousYearPapers(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("CreatePreviousYearPapers request started", "client_ip", clientIP)

	papersInput := new(models.PreviousYearPapersForCreate)
	if err := ctx.ShouldBindJSON(papersInput); err != nil {
		duration := time.Since(start)
		slog.Warn("CreatePreviousYearPapers failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("CreatePreviousYearPapers request",
		"client_ip", clientIP,
		"paper_count", len(papersInput.Papers),
	)

	// Validate that we have papers to create
	if len(papersInput.Papers) == 0 {
		duration := time.Since(start)
		slog.Warn("CreatePreviousYearPapers failed - no papers provided",
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No papers provided"})
		return
	}

	// Additional validation for exam types
	for i, paper := range papersInput.Papers {
		if paper.ExamType != models.ExamTypeIITJEE && paper.ExamType != models.ExamTypeNEET {
			duration := time.Since(start)
			slog.Warn("CreatePreviousYearPapers failed - invalid exam type",
				"client_ip", clientIP,
				"paper_index", i,
				"exam_type", paper.ExamType,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid exam type. Must be either 'IIT-JEE' or 'NEET'",
			})
			return
		}
	}

	createdPapers, err := h.db.CreatePreviousYearPapers(ctx.Request.Context(), papersInput)
	if err != nil {
		duration := time.Since(start)
		slog.Error("CreatePreviousYearPapers failed - database error",
			"client_ip", clientIP,
			"paper_count", len(papersInput.Papers),
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)

		// Check for specific error types
		if err.Error() == "no previous year papers provided" {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("CreatePreviousYearPapers successful",
		"client_ip", clientIP,
		"paper_count", len(createdPapers),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, createdPapers)
}

// GetPreviousYearPapersByExamType godoc
//
//	@Summary		Get Previous Year Papers by Exam Type
//	@Description	get previous year papers filtered by exam type, sorted by year (descending)
//	@Security       BearerAuth
//	@Param			exam_type	query		string	true	"Exam Type (IIT-JEE or NEET)"
//	@Tags			previous-year-papers
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.PreviousYearPaper
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/previous-year-papers [get]
func (h *Handlers) GetPreviousYearPapersByExamType(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetPreviousYearPapersByExamType request started", "client_ip", clientIP)

	examType := ctx.Query("exam_type")
	if examType == "" {
		duration := time.Since(start)
		slog.Warn("GetPreviousYearPapersByExamType failed - missing exam_type parameter",
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing exam_type parameter"})
		return
	}

	// Validate exam type
	if examType != models.ExamTypeIITJEE && examType != models.ExamTypeNEET {
		duration := time.Since(start)
		slog.Warn("GetPreviousYearPapersByExamType failed - invalid exam type",
			"client_ip", clientIP,
			"exam_type", examType,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid exam type. Must be either 'IIT-JEE' or 'NEET'",
		})
		return
	}

	slog.Debug("GetPreviousYearPapersByExamType request",
		"client_ip", clientIP,
		"exam_type", examType,
	)

	papers, err := h.db.GetPreviousYearPapersByExamType(ctx.Request.Context(), examType)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetPreviousYearPapersByExamType failed - database error",
			"client_ip", clientIP,
			"exam_type", examType,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetPreviousYearPapersByExamType successful",
		"client_ip", clientIP,
		"exam_type", examType,
		"paper_count", len(papers),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"papers": papers})
}
