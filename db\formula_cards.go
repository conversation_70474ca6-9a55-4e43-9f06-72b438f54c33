package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// CreateFormulaCards creates multiple formula cards for a subject in a single transaction
func (p *DbPlugin) CreateFormulaCards(ctx context.Context, formulaCardsInput *models.FormulaCardsForCreate) ([]models.FormulaCard, error) {
	start := time.Now()
	slog.Info("Creating formula cards",
		"subject_name", formulaCardsInput.SubjectName,
		"card_count", len(formulaCardsInput.FormulaCards),
	)

	// Validate input before starting transaction
	if len(formulaCardsInput.FormulaCards) == 0 {
		return nil, fmt.Errorf("no formula cards provided")
	}

	// Find the subject by name before starting transaction
	var subject models.Subject
	if err := p.db.Where("name = ?", formulaCardsInput.SubjectName).First(&subject).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Subject not found for formula cards creation",
			"subject_name", formulaCardsInput.SubjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("subject '%s' not found: %w", formulaCardsInput.SubjectName, err)
	}

	// Start transaction for creating formula cards
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			slog.Error("Panic during formula cards creation",
				"subject_name", formulaCardsInput.SubjectName,
				"panic", r,
			)
			tx.Rollback()
		}
	}()

	var createdCards []models.FormulaCard

	// Create each formula card
	for _, cardInput := range formulaCardsInput.FormulaCards {
		formulaCard := models.FormulaCard{
			Name:      cardInput.Name,
			ImageUrl:  cardInput.ImageUrl,
			SubjectID: subject.ID,
		}

		if err := tx.Create(&formulaCard).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to create formula card",
				"subject_name", formulaCardsInput.SubjectName,
				"card_name", cardInput.Name,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to create formula card '%s': %w", cardInput.Name, err)
		}

		// Load the formula card with subject association
		if err := tx.Preload("Subject").First(&formulaCard, formulaCard.ID).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to load formula card with subject association",
				"card_id", formulaCard.ID,
				"card_name", cardInput.Name,
				"subject_name", formulaCardsInput.SubjectName,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to load formula card with subject: %w", err)
		}

		createdCards = append(createdCards, formulaCard)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit formula cards creation transaction",
			"subject_name", formulaCardsInput.SubjectName,
			"card_count", len(formulaCardsInput.FormulaCards),
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Formula cards created successfully",
		"subject_name", formulaCardsInput.SubjectName,
		"subject_id", subject.ID,
		"card_count", len(createdCards),
		"duration_ms", duration.Milliseconds(),
	)

	return createdCards, nil
}

// GetFormulaCardsBySubject retrieves all formula cards for a specific subject
func (p *DbPlugin) GetFormulaCardsBySubject(ctx context.Context, subjectName string) ([]models.FormulaCard, error) {
	start := time.Now()
	slog.Debug("Retrieving formula cards by subject", "subject_name", subjectName)

	var formulaCards []models.FormulaCard

	// Find formula cards by subject name using joins
	err := p.db.Preload("Subject").
		Joins("JOIN subjects ON subjects.id = formula_cards.subject_id").
		Where("subjects.name = ?", subjectName).
		Find(&formulaCards).Error

	duration := time.Since(start)

	if err != nil {
		slog.Error("Failed to retrieve formula cards by subject",
			"subject_name", subjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve formula cards for subject '%s': %w", subjectName, err)
	}

	slog.Debug("Formula cards retrieved successfully",
		"subject_name", subjectName,
		"card_count", len(formulaCards),
		"duration_ms", duration.Milliseconds(),
	)

	return formulaCards, nil
}
