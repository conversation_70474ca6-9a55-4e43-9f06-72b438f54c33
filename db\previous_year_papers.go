package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// CreatePreviousYearPapers creates multiple previous year papers in a single transaction
func (p *DbPlugin) CreatePreviousYearPapers(ctx context.Context, papersInput *models.PreviousYearPapersForCreate) ([]models.PreviousYearPaper, error) {
	start := time.Now()
	slog.Info("Creating previous year papers",
		"paper_count", len(papersInput.Papers),
	)

	// Validate input before starting transaction
	if len(papersInput.Papers) == 0 {
		return nil, fmt.Errorf("no previous year papers provided")
	}

	// Validate exam types
	for i, paper := range papersInput.Papers {
		if paper.ExamType != models.ExamTypeIITJEE && paper.ExamType != models.ExamTypeNEET {
			return nil, fmt.Errorf("invalid exam type '%s' for paper at index %d. Must be either '%s' or '%s'",
				paper.ExamType, i, models.ExamTypeIITJEE, models.ExamTypeNEET)
		}
	}

	var createdPapers []models.PreviousYearPaper

	// Start transaction
	tx := p.db.Begin()
	if tx.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to start transaction for creating previous year papers",
			"error", tx.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to start transaction: %w", tx.Error)
	}

	// Create papers in batch
	for _, paperInput := range papersInput.Papers {
		paper := models.PreviousYearPaper{
			Month:    paperInput.Month,
			Year:     paperInput.Year,
			PdfUrl:   paperInput.PdfUrl,
			ExamType: paperInput.ExamType,
		}

		if err := tx.Create(&paper).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to create previous year paper",
				"month", paperInput.Month,
				"year", paperInput.Year,
				"exam_type", paperInput.ExamType,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to create paper for %s %d/%d: %w",
				paperInput.ExamType, paperInput.Month, paperInput.Year, err)
		}

		createdPapers = append(createdPapers, paper)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit transaction for creating previous year papers",
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Previous year papers created successfully",
		"paper_count", len(createdPapers),
		"duration_ms", duration.Milliseconds(),
	)

	return createdPapers, nil
}

// GetPreviousYearPapersByExamType retrieves previous year papers filtered by exam type, sorted by year (descending) then month (descending)
func (p *DbPlugin) GetPreviousYearPapersByExamType(ctx context.Context, examType string) ([]models.PreviousYearPaper, error) {
	start := time.Now()
	slog.Debug("Retrieving previous year papers by exam type", "exam_type", examType)

	// Validate exam type
	if examType != models.ExamTypeIITJEE && examType != models.ExamTypeNEET {
		return nil, fmt.Errorf("invalid exam type '%s'. Must be either '%s' or '%s'",
			examType, models.ExamTypeIITJEE, models.ExamTypeNEET)
	}

	var papers []models.PreviousYearPaper

	// Query papers filtered by exam type, sorted by year (desc) then month (desc)
	if err := p.db.Where("exam_type = ?", examType).
		Order("year DESC, month DESC").
		Find(&papers).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve previous year papers",
			"exam_type", examType,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve papers for exam type %s: %w", examType, err)
	}

	duration := time.Since(start)
	slog.Info("Previous year papers retrieved successfully",
		"exam_type", examType,
		"paper_count", len(papers),
		"duration_ms", duration.Milliseconds(),
	)

	return papers, nil
}
