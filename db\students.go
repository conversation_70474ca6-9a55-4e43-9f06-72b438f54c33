package db

import (
	"context"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) CreateStudent(ctx context.Context, student *models.Student) (*models.Student, error) {
	start := time.Now()
	slog.Info("Creating student", "email", student.User.Email)

	res := p.db.Create(student)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create student",
			"email", student.User.Email,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Student created successfully",
		"email", student.User.Email,
		"student_id", student.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return student, nil
}

// GetStudentIDByUserID gets the student ID for a given user ID
func (p *DbPlugin) GetStudentIDByUserID(ctx context.Context, userID uint) (uint, error) {
	start := time.Now()
	slog.Debug("Getting student ID by user ID", "user_id", userID)

	var student models.Student
	if err := p.db.Where("user_id = ?", userID).First(&student).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to get student ID by user ID",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return 0, err
	}

	duration := time.Since(start)
	slog.Debug("Student ID retrieved successfully",
		"user_id", userID,
		"student_id", student.ID,
		"duration_ms", duration.Milliseconds(),
	)

	return student.ID, nil
}

func (p *DbPlugin) EnrollStudentInCourse(ctx context.Context, userID,
	courseID uint) (*models.Student, error) {
	start := time.Now()
	slog.Info("Enrolling student in course",
		"user_id", userID,
		"course_id", courseID,
	)

	var student models.Student
	var course models.Course

	if err := p.db.Preload("User").Preload("Courses").
		Where("user_id = ?", userID).First(&student).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student for enrollment",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	if err := p.db.
		Where("id = ?", courseID).First(&course).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve course for enrollment",
			"course_id", courseID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	if err := p.db.Model(&student).Association("Courses").Append(&course); err != nil {
		duration := time.Since(start)
		slog.Error("Failed to enroll student in course",
			"user_id", userID,
			"course_id", courseID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Student enrolled in course successfully",
		"user_id", userID,
		"course_id", courseID,
		"course_name", course.Name,
		"student_email", student.User.Email,
		"duration_ms", duration.Milliseconds(),
	)
	return &student, nil
}

func (p *DbPlugin) GetStudentByUserID(ctx context.Context,
	userID uint) (*models.StudentForCreate, error) {
	start := time.Now()
	slog.Debug("Retrieving student by user ID", "user_id", userID)

	var student models.Student
	if err := p.db.Preload("User").
		Where("user_id = ?", userID).First(&student).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student by user ID",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	studentToReturn := &models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       student.User.FullName,
			Email:          student.User.Email,
			PhoneNumber:    student.User.PhoneNumber,
			ContactAddress: student.User.ContactAddress,
		},
		ParentPhone: student.ParentPhone,
		ParentEmail: student.ParentEmail,
	}

	duration := time.Since(start)
	slog.Debug("Student retrieved successfully",
		"user_id", userID,
		"email", student.User.Email,
		"duration_ms", duration.Milliseconds(),
	)
	return studentToReturn, nil
}
