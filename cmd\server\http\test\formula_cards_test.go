package test

import (
	"encoding/json"
	"net/http"
	"net/url"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreateFormulaCards(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", "Physics Test Formula")
	db.Exec("DELETE FROM subjects WHERE name = ?", "Physics Test Formula")

	// First create a subject to associate formula cards with
	newSubject := models.SubjectForCreate{
		Name:        "Physics Test Formula",
		DisplayName: "Physics",
	}

	subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
	assert.Equal(t, http.StatusOK, subjectRR.Code)

	var createdSubject models.Subject
	err := json.Unmarshal(subjectRR.Body.Bytes(), &createdSubject)
	assert.Nil(t, err)

	// Now create formula cards for this subject
	formulaCardsInput := models.FormulaCardsForCreate{
		SubjectName: "Physics Test Formula",
		FormulaCards: []models.FormulaCardForCreate{
			{
				Name:     "Newton's Second Law",
				ImageUrl: "https://example.com/newton-second-law.png",
			},
			{
				Name:     "Kinetic Energy Formula",
				ImageUrl: "https://example.com/kinetic-energy.png",
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)

	// Check status code
	assert.Equal(t, http.StatusOK, rr.Code)

	// Parse response
	var response map[string][]models.FormulaCard
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)

	formulaCards := response["formula_cards"]
	assert.Len(t, formulaCards, 2)
	assert.Equal(t, "Newton's Second Law", formulaCards[0].Name)
	assert.Equal(t, "https://example.com/newton-second-law.png", formulaCards[0].ImageUrl)
	assert.Equal(t, createdSubject.ID, formulaCards[0].SubjectID)

	// Verify they were persisted in the database
	var fromDB []models.FormulaCard
	err = db.Where("subject_id = ?", createdSubject.ID).Find(&fromDB).Error
	assert.Nil(t, err)
	assert.Len(t, fromDB, 2)
}

func TestGetFormulaCardsBySubject(t *testing.T) {
	// Clean up before test - clean up all test subjects
	testSubjects := []string{"Mathematics Test Formula", "Physics Test Formula", "Chemistry Test Formula"}
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}

	// Create multiple subjects with their formula cards
	subjects := []struct {
		name        string
		displayName string
		cards       []models.FormulaCardForCreate
	}{
		{
			name:        "Mathematics Test Formula",
			displayName: "Mathematics",
			cards: []models.FormulaCardForCreate{
				{
					Name:     "Quadratic Formula",
					ImageUrl: "https://example.com/quadratic-formula.png",
				},
				{
					Name:     "Pythagorean Theorem",
					ImageUrl: "https://example.com/pythagorean-theorem.png",
				},
			},
		},
		{
			name:        "Physics Test Formula",
			displayName: "Physics",
			cards: []models.FormulaCardForCreate{
				{
					Name:     "Newton's Second Law",
					ImageUrl: "https://example.com/newton-second-law.png",
				},
				{
					Name:     "Kinetic Energy Formula",
					ImageUrl: "https://example.com/kinetic-energy.png",
				},
				{
					Name:     "Ohm's Law",
					ImageUrl: "https://example.com/ohms-law.png",
				},
			},
		},
		{
			name:        "Chemistry Test Formula",
			displayName: "Chemistry",
			cards: []models.FormulaCardForCreate{
				{
					Name:     "Ideal Gas Law",
					ImageUrl: "https://example.com/ideal-gas-law.png",
				},
			},
		},
	}

	// Create subjects and their formula cards
	for _, subject := range subjects {
		// Create subject
		newSubject := models.SubjectForCreate{
			Name:        subject.name,
			DisplayName: subject.displayName,
		}

		subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
		assert.Equal(t, http.StatusOK, subjectRR.Code)

		// Create formula cards for this subject
		formulaCardsInput := models.FormulaCardsForCreate{
			SubjectName:  subject.name,
			FormulaCards: subject.cards,
		}

		createRR := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)
		assert.Equal(t, http.StatusOK, createRR.Code)
	}

	// Test getting formula cards for each subject
	for _, subject := range subjects {
		t.Run("Get formula cards for "+subject.displayName, func(t *testing.T) {
			getURL := "/api/formula-cards?subject_name=" + url.QueryEscape(subject.name)
			getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

			// Check status code
			assert.Equal(t, http.StatusOK, getRR.Code)

			// Parse response
			var response map[string][]models.FormulaCard
			err := json.Unmarshal(getRR.Body.Bytes(), &response)
			assert.Nil(t, err)

			formulaCards := response["formula_cards"]
			assert.Len(t, formulaCards, len(subject.cards))

			// Verify the first formula card details
			if len(formulaCards) > 0 {
				assert.Equal(t, subject.cards[0].Name, formulaCards[0].Name)
				assert.Equal(t, subject.cards[0].ImageUrl, formulaCards[0].ImageUrl)
				assert.Equal(t, subject.name, formulaCards[0].Subject.Name)
			}

			// Verify all formula cards belong to the correct subject
			for _, card := range formulaCards {
				assert.Equal(t, subject.name, card.Subject.Name)
			}
		})
	}

	// Test that each subject returns only its own formula cards (cross-verification)
	t.Run("Verify subject isolation", func(t *testing.T) {
		// Get Mathematics formula cards
		mathURL := "/api/formula-cards?subject_name=" + url.QueryEscape("Mathematics Test Formula")
		mathRR := requestExecutionHelper(http.MethodGet, mathURL, nil)

		var mathResponse map[string][]models.FormulaCard
		err := json.Unmarshal(mathRR.Body.Bytes(), &mathResponse)
		assert.Nil(t, err)
		mathCards := mathResponse["formula_cards"]

		// Get Physics formula cards
		physicsURL := "/api/formula-cards?subject_name=" + url.QueryEscape("Physics Test Formula")
		physicsRR := requestExecutionHelper(http.MethodGet, physicsURL, nil)

		var physicsResponse map[string][]models.FormulaCard
		err = json.Unmarshal(physicsRR.Body.Bytes(), &physicsResponse)
		assert.Nil(t, err)
		physicsCards := physicsResponse["formula_cards"]

		// Verify different counts
		assert.Equal(t, 2, len(mathCards))
		assert.Equal(t, 3, len(physicsCards))

		// Verify no cross-contamination
		for _, card := range mathCards {
			assert.Equal(t, "Mathematics Test Formula", card.Subject.Name)
		}
		for _, card := range physicsCards {
			assert.Equal(t, "Physics Test Formula", card.Subject.Name)
		}
	})
}

func TestCreateFormulaCardsInvalidSubject(t *testing.T) {
	// Try to create formula cards for a non-existent subject
	formulaCardsInput := models.FormulaCardsForCreate{
		SubjectName: "NonExistentSubject",
		FormulaCards: []models.FormulaCardForCreate{
			{
				Name:     "Test Formula",
				ImageUrl: "https://example.com/test.png",
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)

	// Should return 404 for non-existent subject
	assert.Equal(t, http.StatusNotFound, rr.Code)

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, "Subject not found", response["error"])
}

func TestGetFormulaCardsBySubjectEmpty(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", "Biology Test Formula")
	db.Exec("DELETE FROM subjects WHERE name = ?", "Biology Test Formula")

	// Create a subject without formula cards
	newSubject := models.SubjectForCreate{
		Name:        "Biology Test Formula",
		DisplayName: "Biology",
	}

	subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
	assert.Equal(t, http.StatusOK, subjectRR.Code)

	// Get formula cards for this subject (should be empty)
	getURL := "/api/formula-cards?subject_name=" + url.QueryEscape("Biology Test Formula")
	getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

	// Check status code
	assert.Equal(t, http.StatusOK, getRR.Code)

	// Parse response
	var response map[string][]models.FormulaCard
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)

	formulaCards := response["formula_cards"]
	assert.Len(t, formulaCards, 0)
}
